import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import Loading from 'src/components/Loading';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import { UserWithPermissionsAndRoles } from 'src/types/bootstrapTypes';
import { useFavoriteStatus } from 'src/views/CRUD/hooks';
import { Dashboard } from 'src/pages/DashboardList';
import { useFolder } from '../hooks/useFolder';
import { FolderSlug } from '../types';
import { DashboardFolders } from '../new_components';
import { createBusinessUnitFromFolders, extractDashboardIds } from '../utils';

interface IProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canExport: boolean;
  };
  actions: {
    onDeleteDashboard: (dashboard: Dashboard) => void;
    onExportDashboard: (dashboard: Dashboard[]) => void;
    onEditDashboard: (dashboard: Dashboard) => void;
  };
}

const AdaptedFolderViewMode = ({ actions, permissions }: IProps) => {
  const { addDangerToast } = useToasts();
  const user = useSelector<any, UserWithPermissionsAndRoles>(
    state => state.user,
  );
  const isAdmin = Boolean(user.roles?.Admin);

  const { result: globalFolder, status: globalStatus } = useFolder(
    FolderSlug.Global,
  );

  const dashboardIds = useMemo(
    () => extractDashboardIds(globalFolder),
    [globalFolder],
  );

  const [saveFavoriteStatus, favoriteStatus] = useFavoriteStatus(
    'dashboard',
    dashboardIds,
    addDangerToast,
  );

  const businessData = createBusinessUnitFromFolders(
    globalFolder,
    favoriteStatus,
  );

  const isLoading = globalStatus === 'loading';

  if (isLoading) {
    return <Loading />;
  }

  const { canEdit, canDelete, canExport } = permissions;
  const { onDeleteDashboard, onExportDashboard, onEditDashboard } = actions;

  return (
    <DashboardFolders
      domainData={businessData}
      saveFavoriteStatus={saveFavoriteStatus}
      onDeleteDashboard={canDelete ? onDeleteDashboard : undefined}
      onExportDashboard={canExport ? onExportDashboard : undefined}
      onEditDashboard={canEdit ? onEditDashboard : undefined}
      canEditFolder={isAdmin}
    />
  );
};

export default AdaptedFolderViewMode;
