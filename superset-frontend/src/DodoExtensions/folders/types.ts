import { Owner } from '@superset-ui/chart-controls';

export enum EntityType {
  Folder = 0,
  Dashboard = 1,
}

export enum FolderSlug {
  Global = 'global',
  Plugin = 'plugin',
  Personal = 'personal',
  PluginAnalytics = 'plugin_analytics',
  PluginAnalyticsPizza = 'plugin_analytics_pizza',
  PluginAnalyticsDrinkit = 'plugin_analytics_drinkit',
  GlobalPizza = 'global_pizza',
  GlobalDrinkit = 'global_drinkit',
  Team = 'team',
}

export enum FolderType {
  Global = 0,
  Team = 1,
  Plugin = 2,
  Personal = 3,
}

export enum FolderLevel {
  Root = 0,
  System = 1,
  User = 2,
}

export interface Folder {
  id: number;
  item_type: EntityType.Folder;
  folder_type: FolderType;
  name_ru: string;
  name_en: string;
  description_ru: string;
  description_en: string;
  children: Entity[];
  slug: FolderSlug | null;
  level: FolderLevel;
  parent_id: number | null;
}

export interface Dashboard {
  id: number;
  item_type: EntityType.Dashboard;
  name_ru: string;
  name_en: string;
  certified_by: string;
  parent_id: number;
  changed_on: string;
  owners: Owner[];
  published: boolean;
  tags: { id: number; name: string; type: number }[];
}

export type Entity = Folder | Dashboard;

export interface TreeNodeData {
  key: string;
  title: string;
  entity: Entity;
  id: number;
  item_type: EntityType;
  selectable: boolean;
  isLeaf: boolean;
  children?: TreeNodeData[];
  searchTerm?: string;
}

export interface FolderListDashboard {
  id: number;
  url: string;
  dashboard_title: string;
  dashboard_title_ru: string;
  certified_by: string;
}

export const isFolderType = (entity: unknown): entity is Folder =>
  entity !== null &&
  typeof entity === 'object' &&
  'item_type' in entity &&
  typeof (entity as { item_type: unknown }).item_type === 'number' &&
  (entity as { item_type: number }).item_type === EntityType.Folder;

export interface DashboardItem {
  id: number;
  name_ru: string;
  name_en: string;
  changedOn: string;
  published: boolean;
  isFavorite: boolean;
  certifiedBy: string;
  owners: Owner[];
  tags: { id: number; name: string; type: number }[];
}
export interface FolderData {
  id: string;
  title: string;
  subtitle: string;
  dashboardCount: number;
  icon: React.ComponentType<any>;
  tags: string[];
  category: string;
  dashboards: DashboardItem[];
  subfolders?: FolderData[];
  isExpanded?: boolean;
}
export interface BusinessDomain {
  id: string;
  name: string;
  color: string;
  secondaryColor: string;
  folders: FolderData[];
}
