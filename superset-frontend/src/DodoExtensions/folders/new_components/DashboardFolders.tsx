import { useState, useEffect, Fragment, useMemo } from 'react';
import { FolderOutlined } from '@ant-design/icons';
import { styled, t } from '@superset-ui/core';
import BusinessUnitSelector from './BusinessDomainSelector';
import FolderHeader from './FolderHeader';
import BreadcrumbNavigation from './BreadcrumbNavigation';
import FoldersSection from './FoldersSection';
import DashboardsSection from './DashboardsSection';
import Folder from './Folder';
import { BusinessDomain } from '../types';

interface DashboardFoldersProps {
  domainData: BusinessDomain[];
  canEditFolder: boolean;
  saveFavoriteStatus: (id: number, isStarred: boolean) => void;
  onDeleteDashboard?: (dashboard: any) => void;
  onExportDashboard?: (dashboard: any) => void;
  onEditDashboard?: (dashboard: any) => void;
}

const MainContainer = styled.div`
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
`;

const ContentArea = styled.div`
  flex: 1;
  display: flex;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
`;

const Sidebar = styled.div`
  width: 280px;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};
  border-right: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  display: flex;
  flex-direction: column;
`;

const FolderList = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const DashboardGrid = styled.div`
  flex: 1;
  padding: ${({ theme }) => theme.gridUnit * 6}px;
  overflow-y: auto;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
`;

const EmptyStateIcon = styled.div`
  width: 80px;
  height: 80px;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  border: 2px dashed ${({ theme }) => theme.colors.grayscale.light2};
`;

const EmptyStateIconContent = styled(FolderOutlined)`
  font-size: 32px;
  color: ${({ theme }) => theme.colors.text.label};
`;

const EmptyStateTitle = styled.span`
  color: ${({ theme }) => theme.colors.text.label};
  margin-bottom: ${({ theme }) => theme.gridUnit * 2}px;
`;

const EmptyStateText = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  color: ${({ theme }) => theme.colors.text.label};
`;

const DashboardFolders = ({
  domainData,
  canEditFolder,
  saveFavoriteStatus,
  onDeleteDashboard,
  onExportDashboard,
  onEditDashboard,
}: DashboardFoldersProps) => {
  const [selectedDomain, setSelectedDomain] = useState(
    domainData[0]?.id || 'pizza',
  );
  const [selectedFolder, setSelectedFolder] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(),
  );

  const currentBusiness =
    domainData.find(b => b.id === selectedDomain) || domainData[0];
  const currentFolders = useMemo(
    () => currentBusiness?.folders || [],
    [currentBusiness],
  );

  const findFolderById = (folderId: string, folders: any[]): any => {
    for (const folder of folders) {
      if (folder.id === folderId) {
        return folder;
      }
      if (folder.subfolders) {
        const found = findFolderById(folderId, folder.subfolders);
        if (found) return found;
      }
    }
    return null;
  };

  const toggleFolderExpansion = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const buildBreadcrumbPath = (folderId: string, folders: any[]): any[] => {
    for (const folder of folders) {
      if (folder.id === folderId) {
        return [folder];
      }
      if (folder.subfolders) {
        const subPath = buildBreadcrumbPath(folderId, folder.subfolders);
        if (subPath.length > 0) {
          return [folder, ...subPath];
        }
      }
    }
    return [];
  };

  const handleBreadcrumbClick = (folderId?: string) => {
    if (folderId) {
      setSelectedFolder(folderId);
    } else {
      // Clicked on business unit - go back to business unit view
      setSelectedFolder('');
    }
  };

  // Set default folder when business changes
  useEffect(() => {
    if (currentFolders.length > 0 && !selectedFolder) {
      setSelectedFolder(currentFolders[0].id);
    }
  }, [selectedDomain, currentFolders, selectedFolder]);

  const totalDashboards = currentFolders.reduce(
    (sum, folder) => sum + folder.dashboardCount,
    0,
  );
  const currentFolder = findFolderById(selectedFolder, currentFolders);

  const dashboards = currentFolder?.dashboards || [];
  const subfolders = currentFolder?.subfolders || [];

  // Recursive function to render folders and subfolders (max depth: 5)
  const renderFolder = (folder: any, level = 0) => {
    if (level >= 5) {
      return null;
    }

    const isSelected = selectedFolder === folder.id;
    const businessColor = currentBusiness.color;
    const lightBusinessColor = currentBusiness.secondaryColor;
    const isExpanded = expandedFolders.has(folder.id);
    const hasSubfolders =
      folder.subfolders && folder.subfolders.length > 0 && level < 4; // Only show expand if not at max depth
    const indentLevel = level * 16;

    return (
      <Fragment key={folder.id}>
        {/* Main folder item */}
        <Folder
          folderId={folder.id}
          folderTitle={folder.title}
          folderSubtitle={folder.subtitle}
          folderDashboardCount={folder.dashboardCount}
          isSelected={isSelected}
          businessColor={businessColor}
          lightBusinessColor={lightBusinessColor}
          indentLevel={indentLevel}
          hasSubfolders={hasSubfolders}
          isExpanded={isExpanded}
          toggleFolderExpansion={toggleFolderExpansion}
          setSelectedFolder={setSelectedFolder}
        />

        {/* Render subfolders if expanded */}
        {hasSubfolders && isExpanded && (
          <div>
            {folder.subfolders.map((subfolder: any) =>
              renderFolder(subfolder, level + 1),
            )}
          </div>
        )}
      </Fragment>
    );
  };

  return (
    <MainContainer>
      <ContentArea>
        <Sidebar>
          <BusinessUnitSelector
            domainData={domainData}
            selectedDomain={selectedDomain}
            onBusinessChange={(businessId: string) => {
              setSelectedDomain(businessId);
              setSelectedFolder('');
            }}
            totalDashboards={totalDashboards}
            currentBusiness={currentBusiness}
            canEditFolder={canEditFolder}
          />

          <FolderList>
            {currentFolders.map(folder => renderFolder(folder, 0))}
          </FolderList>
        </Sidebar>

        <MainContent>
          <FolderHeader
            currentFolder={currentFolder}
            currentBusiness={currentBusiness}
          />

          <BreadcrumbNavigation
            currentBusiness={currentBusiness}
            selectedFolder={selectedFolder}
            currentFolders={currentFolders}
            onBreadcrumbClick={handleBreadcrumbClick}
            buildBreadcrumbPath={buildBreadcrumbPath}
          />

          <DashboardGrid>
            {!currentFolder ? (
              <EmptyState>
                <EmptyStateIcon>
                  <EmptyStateIconContent />
                </EmptyStateIcon>
                <EmptyStateTitle>
                  {t('Select a folder to view dashboards')}
                </EmptyStateTitle>
                <EmptyStateText>
                  {t(
                    'Choose a folder from the left panel to see its dashboards and analytics',
                  )}
                </EmptyStateText>
              </EmptyState>
            ) : (
              <div>
                <FoldersSection
                  subfolders={subfolders}
                  currentBusiness={currentBusiness}
                  onFolderClick={setSelectedFolder}
                />

                <DashboardsSection
                  dashboards={dashboards}
                  currentBusiness={currentBusiness}
                  saveFavoriteStatus={saveFavoriteStatus}
                  onDeleteDashboard={onDeleteDashboard}
                  onExportDashboard={onExportDashboard}
                  onEditDashboard={onEditDashboard}
                />
              </div>
            )}
          </DashboardGrid>
        </MainContent>
      </ContentArea>
    </MainContainer>
  );
};

export default DashboardFolders;
