import { Link } from 'react-router-dom';
import { Button } from 'antd';
import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import { BusinessDomain } from '../types';

interface BusinessDomainSelectorProps {
  domainData: BusinessDomain[];
  selectedDomain: string;
  onBusinessChange: (businessId: string) => void;
  totalDashboards: number;
  currentBusiness?: BusinessDomain;
  canEditFolder?: boolean;
}

const SelectorContainer = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 4}px
    ${({ theme }) => theme.gridUnit * 4}px
    ${({ theme }) => theme.gridUnit * 3}px;
  height: 125px;
`;

const SelectorTitle = styled.h4`
  margin: 0 0 ${({ theme }) => theme.gridUnit * 3}px 0;
  font-size: ${({ theme }) => theme.typography.sizes.l}px;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
  margin-bottom: ${({ theme }) => theme.gridUnit * 3}px;
`;

const StyledButton = styled(Button)<{
  businessColor?: string;
  isSelected?: boolean;
}>`
  background-color: ${({ isSelected, businessColor }) =>
    isSelected ? businessColor : 'transparent'};
  border-color: ${({ businessColor }) => businessColor};
  color: ${({ isSelected, businessColor, theme }) =>
    isSelected ? theme.colors.grayscale.light5 : businessColor};
  font-weight: ${({ theme }) => theme.typography.weights.medium};
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  text-transform: uppercase;

  a {
    margin-left: ${({ theme }) => theme.gridUnit}px;
    color: ${({ isSelected, businessColor, theme }) =>
      isSelected ? theme.colors.grayscale.light5 : businessColor};
    transition: color 0.2s ease;
  }

  &:hover {
    background-color: ${({ isSelected, businessColor }) =>
      isSelected ? businessColor : 'transparent'};
    border-color: ${({ businessColor }) => businessColor};
    color: ${({ isSelected, businessColor, theme }) =>
      isSelected ? theme.colors.grayscale.light5 : businessColor};
  }

  &:focus {
    background-color: ${({ isSelected, businessColor }) =>
      isSelected ? businessColor : 'transparent'};
    border-color: ${({ businessColor }) => businessColor};
    color: ${({ isSelected, businessColor, theme }) =>
      isSelected ? theme.colors.grayscale.light5 : businessColor};
  }
`;

const SummaryText = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  color: ${({ theme }) => theme.colors.text.label};
`;

const BusinessDomainSelector = ({
  domainData,
  selectedDomain,
  onBusinessChange,
  totalDashboards,
  currentBusiness,
  canEditFolder,
}: BusinessDomainSelectorProps) => (
  <SelectorContainer>
    <SelectorTitle>{t('Business domain')}</SelectorTitle>
    <ButtonContainer>
      {domainData.map(business => (
        <StyledButton
          key={business.id}
          size="small"
          type={selectedDomain === business.id ? 'primary' : 'default'}
          onClick={() => onBusinessChange(business.id)}
          businessColor={business.color}
          isSelected={selectedDomain === business.id}
        >
          {business.name}
          {canEditFolder && (
            <Link
              to="/folder/global"
              target="_blank"
              onClick={e => e.stopPropagation()}
            >
              <Icons.EditOutlined iconSize="m" />
            </Link>
          )}
        </StyledButton>
      ))}
    </ButtonContainer>
    <SummaryText>
      {totalDashboards} {t('dashboards in')} {currentBusiness?.name}
    </SummaryText>
  </SelectorContainer>
);

export default BusinessDomainSelector;
